"""
Unit Tests for Data Generation Module

Tests the synthetic data generation functionality to ensure data quality
and consistency.

Author: Intel ISEF Project - NeuroToxPredict
"""

import unittest
import pandas as pd
import numpy as np
import sys
import os

# Add src directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from data_generation import NeuroToxDataGenerator


class TestNeuroToxDataGenerator(unittest.TestCase):
    """Test cases for the NeuroToxDataGenerator class."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.generator = NeuroToxDataGenerator(random_seed=42)
        self.n_compounds = 100  # Small dataset for testing
    
    def test_initialization(self):
        """Test generator initialization."""
        self.assertIsInstance(self.generator, NeuroToxDataGenerator)
        self.assertIsInstance(self.generator.descriptor_ranges, dict)
        self.assertGreater(len(self.generator.descriptor_ranges), 0)
    
    def test_molecular_descriptors_generation(self):
        """Test molecular descriptor generation."""
        descriptors = self.generator.generate_molecular_descriptors(self.n_compounds)
        
        # Check DataFrame properties
        self.assertIsInstance(descriptors, pd.DataFrame)
        self.assertEqual(len(descriptors), self.n_compounds)
        self.assertIn('compound_id', descriptors.columns)
        self.assertIn('molecular_weight', descriptors.columns)
        self.assertIn('logp', descriptors.columns)
        self.assertIn('bbb_permeability', descriptors.columns)
        
        # Check data types
        self.assertTrue(descriptors['molecular_weight'].dtype in [np.float64, np.int64])
        self.assertTrue(descriptors['logp'].dtype == np.float64)
        
        # Check value ranges
        self.assertTrue(descriptors['molecular_weight'].min() >= 150)
        self.assertTrue(descriptors['molecular_weight'].max() <= 800)
        self.assertTrue(descriptors['logp'].min() >= -2)
        self.assertTrue(descriptors['logp'].max() <= 6)
        self.assertTrue(descriptors['bbb_permeability'].min() >= 0)
        self.assertTrue(descriptors['bbb_permeability'].max() <= 1)
        
        # Check for missing values
        self.assertEqual(descriptors.isnull().sum().sum(), 0)
        
        # Check compound IDs are unique
        self.assertEqual(len(descriptors['compound_id'].unique()), self.n_compounds)
    
    def test_neurotoxicity_labels_generation(self):
        """Test neurotoxicity label generation."""
        descriptors = self.generator.generate_molecular_descriptors(self.n_compounds)
        full_dataset = self.generator.generate_neurotoxicity_labels(descriptors)
        
        # Check that new columns are added
        self.assertIn('neurotoxic', full_dataset.columns)
        self.assertIn('neurotox_probability', full_dataset.columns)
        
        # Check binary classification
        unique_labels = full_dataset['neurotoxic'].unique()
        self.assertTrue(set(unique_labels).issubset({0, 1}))
        
        # Check probability range
        self.assertTrue(full_dataset['neurotox_probability'].min() >= 0)
        self.assertTrue(full_dataset['neurotox_probability'].max() <= 1)
        
        # Check consistency between probability and binary label
        high_prob = full_dataset['neurotox_probability'] > 0.5
        binary_labels = full_dataset['neurotoxic'] == 1
        # Allow some tolerance due to threshold adjustment
        consistency = (high_prob == binary_labels).mean()
        self.assertGreater(consistency, 0.6)  # At least 60% consistency
    
    def test_reproducibility(self):
        """Test that results are reproducible with same random seed."""
        generator1 = NeuroToxDataGenerator(random_seed=42)
        generator2 = NeuroToxDataGenerator(random_seed=42)
        
        descriptors1 = generator1.generate_molecular_descriptors(50)
        descriptors2 = generator2.generate_molecular_descriptors(50)
        
        # Check that results are identical
        pd.testing.assert_frame_equal(descriptors1, descriptors2)
    
    def test_different_sample_sizes(self):
        """Test generation with different sample sizes."""
        for n in [10, 50, 100]:
            descriptors = self.generator.generate_molecular_descriptors(n)
            self.assertEqual(len(descriptors), n)
            
            full_dataset = self.generator.generate_neurotoxicity_labels(descriptors)
            self.assertEqual(len(full_dataset), n)
    
    def test_feature_correlations(self):
        """Test that expected feature correlations exist."""
        descriptors = self.generator.generate_molecular_descriptors(1000)
        
        # BBB permeability should correlate with LogP
        bbb_logp_corr = descriptors['bbb_permeability'].corr(descriptors['logp'])
        self.assertGreater(bbb_logp_corr, 0.3)  # Positive correlation
        
        # Polar surface area should negatively correlate with LogP
        psa_logp_corr = descriptors['polar_surface_area'].corr(descriptors['logp'])
        self.assertLess(psa_logp_corr, -0.2)  # Negative correlation
        
        # Molecular weight should correlate with complexity
        mw_complexity_corr = descriptors['molecular_weight'].corr(descriptors['complexity'])
        self.assertGreater(mw_complexity_corr, 0.2)  # Positive correlation
    
    def test_class_balance(self):
        """Test that class distribution is reasonable."""
        descriptors = self.generator.generate_molecular_descriptors(1000)
        full_dataset = self.generator.generate_neurotoxicity_labels(descriptors)
        
        positive_ratio = full_dataset['neurotoxic'].mean()
        
        # Should have reasonable class balance (not too extreme)
        self.assertGreater(positive_ratio, 0.1)  # At least 10% positive
        self.assertLess(positive_ratio, 0.9)     # At most 90% positive
        
        # Should be closer to 30% positive as designed
        self.assertGreater(positive_ratio, 0.2)
        self.assertLess(positive_ratio, 0.5)
    
    def test_data_quality(self):
        """Test overall data quality."""
        descriptors = self.generator.generate_molecular_descriptors(500)
        full_dataset = self.generator.generate_neurotoxicity_labels(descriptors)
        
        # No missing values
        self.assertEqual(full_dataset.isnull().sum().sum(), 0)
        
        # No duplicate compound IDs
        self.assertEqual(len(full_dataset['compound_id'].unique()), len(full_dataset))
        
        # Reasonable feature variability
        numeric_cols = full_dataset.select_dtypes(include=[np.number]).columns
        for col in numeric_cols:
            if col not in ['neurotoxic']:  # Skip binary target
                cv = full_dataset[col].std() / full_dataset[col].mean()
                self.assertGreater(cv, 0.01)  # Some variability
                self.assertLess(cv, 10)       # Not too extreme


class TestDataGenerationIntegration(unittest.TestCase):
    """Integration tests for the complete data generation pipeline."""
    
    def test_full_pipeline(self):
        """Test the complete data generation pipeline."""
        generator = NeuroToxDataGenerator(random_seed=123)
        
        # Generate descriptors
        descriptors = generator.generate_molecular_descriptors(200)
        
        # Generate labels
        full_dataset = generator.generate_neurotoxicity_labels(descriptors)
        
        # Verify final dataset
        self.assertEqual(len(full_dataset), 200)
        self.assertGreater(len(full_dataset.columns), 15)
        self.assertIn('neurotoxic', full_dataset.columns)
        self.assertIn('compound_id', full_dataset.columns)
        
        # Check that neurotoxicity correlates with BBB permeability
        bbb_neurotox_corr = full_dataset['bbb_permeability'].corr(full_dataset['neurotoxic'])
        self.assertGreater(bbb_neurotox_corr, 0.3)  # Should be positive correlation
    
    def test_save_and_load_consistency(self):
        """Test that saved and loaded data is consistent."""
        import tempfile
        import os
        
        generator = NeuroToxDataGenerator(random_seed=456)
        descriptors = generator.generate_molecular_descriptors(100)
        full_dataset = generator.generate_neurotoxicity_labels(descriptors)
        
        # Save to temporary file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
            temp_path = f.name
            full_dataset.to_csv(temp_path, index=False)
        
        try:
            # Load and compare
            loaded_dataset = pd.read_csv(temp_path)
            pd.testing.assert_frame_equal(full_dataset, loaded_dataset)
        finally:
            # Clean up
            os.unlink(temp_path)


if __name__ == '__main__':
    # Run tests
    unittest.main(verbosity=2)
