["tests/test_data_generation.py::TestDataGenerationIntegration::test_full_pipeline", "tests/test_data_generation.py::TestDataGenerationIntegration::test_save_and_load_consistency", "tests/test_data_generation.py::TestNeuroToxDataGenerator::test_class_balance", "tests/test_data_generation.py::TestNeuroToxDataGenerator::test_data_quality", "tests/test_data_generation.py::TestNeuroToxDataGenerator::test_different_sample_sizes", "tests/test_data_generation.py::TestNeuroToxDataGenerator::test_feature_correlations", "tests/test_data_generation.py::TestNeuroToxDataGenerator::test_initialization", "tests/test_data_generation.py::TestNeuroToxDataGenerator::test_molecular_descriptors_generation", "tests/test_data_generation.py::TestNeuroToxDataGenerator::test_neurotoxicity_labels_generation", "tests/test_data_generation.py::TestNeuroToxDataGenerator::test_reproducibility"]