#!/usr/bin/env python3
"""
Simple test script to verify the NeuroToxPredict system works correctly.
"""

import sys
import os
import pandas as pd

# Add src directory to path
sys.path.append('src')

def test_data_generation():
    """Test data generation module."""
    print("Testing data generation...")
    
    from data_generation import NeuroToxDataGenerator
    
    # Generate small dataset
    generator = NeuroToxDataGenerator(random_seed=42)
    descriptors = generator.generate_molecular_descriptors(n_compounds=100)
    full_dataset = generator.generate_neurotoxicity_labels(descriptors)
    
    # Save test dataset
    os.makedirs('data', exist_ok=True)
    full_dataset.to_csv('data/test_data.csv', index=False)
    
    print(f"✓ Generated {len(full_dataset)} compounds")
    print(f"✓ Features: {len(full_dataset.columns)}")
    print(f"✓ Neurotoxic: {full_dataset['neurotoxic'].sum()} ({full_dataset['neurotoxic'].mean():.1%})")
    
    return full_dataset

def test_feature_engineering():
    """Test feature engineering module."""
    print("\nTesting feature engineering...")
    
    from feature_engineering import FeatureEngineer
    
    # Load test data
    df = pd.read_csv('data/test_data.csv')
    
    # Initialize feature engineer
    fe = FeatureEngineer()
    
    # Prepare features
    X, y = fe.prepare_features(df, scale_features=True)
    
    # Split data
    data_splits = fe.split_data(X, y)
    
    print(f"✓ Processed {len(X)} samples")
    print(f"✓ Features: {len(X.columns)}")
    print(f"✓ Train/Val/Test: {len(data_splits['X_train'])}/{len(data_splits['X_val'])}/{len(data_splits['X_test'])}")
    
    return data_splits

def test_models():
    """Test model training."""
    print("\nTesting model training...")
    
    from models import NeuroToxPredictor
    
    # Load test data splits
    df = pd.read_csv('data/test_data.csv')
    from feature_engineering import FeatureEngineer
    fe = FeatureEngineer()
    X, y = fe.prepare_features(df, scale_features=True)
    data_splits = fe.split_data(X, y)
    
    # Initialize predictor
    predictor = NeuroToxPredictor(random_state=42)
    
    # Train models (quick version)
    training_results = predictor.train_models(
        data_splits['X_train'], data_splits['y_train'],
        data_splits['X_val'], data_splits['y_val']
    )
    
    # Evaluate on test set
    test_results = predictor.evaluate_test_set(
        data_splits['X_test'], data_splits['y_test']
    )
    
    print("✓ Model training completed")
    print("✓ Test Results:")
    for model_name, results in test_results.items():
        acc = results['metrics']['accuracy']
        print(f"   {model_name}: {acc:.3f} accuracy")
    
    return test_results

def test_visualization():
    """Test visualization module."""
    print("\nTesting visualization...")
    
    from visualization import NeuroToxVisualizer
    
    # Load test data
    df = pd.read_csv('data/test_data.csv')
    
    # Initialize visualizer
    visualizer = NeuroToxVisualizer(output_dir='test_results')
    
    # Create basic plots
    visualizer.plot_data_distribution(df)
    visualizer.plot_correlation_matrix(df)
    
    print("✓ Basic visualizations created")
    print(f"✓ Figures saved to: {visualizer.figure_dir}")
    
    return True

def main():
    """Run all tests."""
    print("NeuroToxPredict System Test")
    print("=" * 40)
    
    try:
        # Test each component
        dataset = test_data_generation()
        data_splits = test_feature_engineering()
        test_results = test_models()
        test_visualization()
        
        print("\n" + "=" * 40)
        print("✓ ALL TESTS PASSED!")
        print("✓ System is working correctly")
        print("\nNext steps:")
        print("1. Run full pipeline with: python main.py --quick-run")
        print("2. Explore Jupyter notebooks in notebooks/")
        print("3. Check results in test_results/")
        
    except Exception as e:
        print(f"\n❌ TEST FAILED: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
