"""
Visualization Module for Neurotoxicity Prediction Analysis

This module creates comprehensive visualizations for model performance,
feature importance, and data analysis for the ISEF project.

Author: Intel ISEF Project - NeuroToxPredict
"""

import matplotlib.pyplot as plt
import seaborn as sns
import pandas as pd
import numpy as np
from sklearn.metrics import confusion_matrix, roc_curve, auc, precision_recall_curve
from sklearn.tree import plot_tree
import joblib
import os
from typing import Dict, List, Tuple
import warnings
warnings.filterwarnings('ignore')

# Set style for publication-quality plots
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")


class NeuroToxVisualizer:
    """
    Creates comprehensive visualizations for neurotoxicity prediction analysis.
    """
    
    def __init__(self, output_dir: str = 'results'):
        """Initialize the visualizer."""
        self.output_dir = output_dir
        self.figure_dir = os.path.join(output_dir, 'figures')
        os.makedirs(self.figure_dir, exist_ok=True)
        
        # Set default figure parameters
        plt.rcParams['figure.figsize'] = (10, 6)
        plt.rcParams['font.size'] = 12
        plt.rcParams['axes.titlesize'] = 14
        plt.rcParams['axes.labelsize'] = 12
        plt.rcParams['xtick.labelsize'] = 10
        plt.rcParams['ytick.labelsize'] = 10
        plt.rcParams['legend.fontsize'] = 10
    
    def plot_data_distribution(self, df: pd.DataFrame, save: bool = True) -> plt.Figure:
        """
        Plot distribution of key molecular descriptors and target variable.
        
        Args:
            df: Dataset DataFrame
            save: Whether to save the figure
            
        Returns:
            Matplotlib figure
        """
        fig, axes = plt.subplots(2, 3, figsize=(15, 10))
        fig.suptitle('Distribution of Key Molecular Descriptors', fontsize=16, fontweight='bold')
        
        # Key descriptors to visualize
        descriptors = [
            'molecular_weight', 'logp', 'polar_surface_area',
            'bbb_permeability', 'toxicity_score', 'neurotoxic'
        ]
        
        for i, descriptor in enumerate(descriptors):
            row, col = i // 3, i % 3
            ax = axes[row, col]
            
            if descriptor == 'neurotoxic':
                # Bar plot for target variable
                counts = df[descriptor].value_counts()
                ax.bar(['Non-Neurotoxic', 'Neurotoxic'], counts.values, 
                      color=['lightblue', 'salmon'])
                ax.set_ylabel('Count')
                ax.set_title('Target Variable Distribution')
                
                # Add percentage labels
                total = len(df)
                for j, count in enumerate(counts.values):
                    ax.text(j, count + total*0.01, f'{count/total:.1%}', 
                           ha='center', va='bottom', fontweight='bold')
            else:
                # Histogram for continuous variables
                ax.hist(df[descriptor], bins=30, alpha=0.7, edgecolor='black')
                ax.set_xlabel(descriptor.replace('_', ' ').title())
                ax.set_ylabel('Frequency')
                ax.set_title(f'{descriptor.replace("_", " ").title()} Distribution')
                
                # Add statistics
                mean_val = df[descriptor].mean()
                ax.axvline(mean_val, color='red', linestyle='--', alpha=0.8, 
                          label=f'Mean: {mean_val:.2f}')
                ax.legend()
        
        plt.tight_layout()
        
        if save:
            plt.savefig(os.path.join(self.figure_dir, 'data_distribution.png'), 
                       dpi=300, bbox_inches='tight')
        
        return fig
    
    def plot_correlation_matrix(self, df: pd.DataFrame, save: bool = True) -> plt.Figure:
        """
        Plot correlation matrix of molecular descriptors.
        
        Args:
            df: Dataset DataFrame
            save: Whether to save the figure
            
        Returns:
            Matplotlib figure
        """
        # Select numeric columns only
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        numeric_cols = [col for col in numeric_cols if col not in ['compound_id']]
        
        # Calculate correlation matrix
        corr_matrix = df[numeric_cols].corr()
        
        # Create figure
        fig, ax = plt.subplots(figsize=(12, 10))
        
        # Create heatmap
        mask = np.triu(np.ones_like(corr_matrix, dtype=bool))
        sns.heatmap(corr_matrix, mask=mask, annot=True, cmap='coolwarm', center=0,
                   square=True, linewidths=0.5, cbar_kws={"shrink": 0.8}, ax=ax)
        
        ax.set_title('Molecular Descriptor Correlation Matrix', fontsize=16, fontweight='bold')
        plt.xticks(rotation=45, ha='right')
        plt.yticks(rotation=0)
        
        if save:
            plt.savefig(os.path.join(self.figure_dir, 'correlation_matrix.png'), 
                       dpi=300, bbox_inches='tight')
        
        return fig
    
    def plot_feature_importance(self, importance_data: Dict[str, pd.DataFrame], 
                               save: bool = True) -> plt.Figure:
        """
        Plot feature importance for all models.
        
        Args:
            importance_data: Dictionary with model names and importance DataFrames
            save: Whether to save the figure
            
        Returns:
            Matplotlib figure
        """
        n_models = len(importance_data)
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('Feature Importance Across Models', fontsize=16, fontweight='bold')
        
        axes = axes.flatten()
        
        for i, (model_name, importance_df) in enumerate(importance_data.items()):
            if i >= 4:  # Only plot first 4 models
                break
                
            ax = axes[i]
            
            # Get top 10 features
            top_features = importance_df.head(10)
            
            # Create horizontal bar plot
            bars = ax.barh(range(len(top_features)), top_features['importance'])
            ax.set_yticks(range(len(top_features)))
            ax.set_yticklabels(top_features['feature'])
            ax.set_xlabel('Importance Score')
            ax.set_title(f'{model_name.replace("_", " ").title()}')
            
            # Color bars by importance
            colors = plt.cm.viridis(top_features['importance'] / top_features['importance'].max())
            for bar, color in zip(bars, colors):
                bar.set_color(color)
            
            # Add value labels
            for j, (idx, row) in enumerate(top_features.iterrows()):
                ax.text(row['importance'] + 0.01, j, f'{row["importance"]:.3f}', 
                       va='center', fontsize=9)
            
            ax.invert_yaxis()
        
        # Hide unused subplots
        for i in range(len(importance_data), 4):
            axes[i].set_visible(False)
        
        plt.tight_layout()
        
        if save:
            plt.savefig(os.path.join(self.figure_dir, 'feature_importance.png'), 
                       dpi=300, bbox_inches='tight')
        
        return fig
    
    def plot_model_performance(self, test_results: Dict[str, Dict], save: bool = True) -> plt.Figure:
        """
        Plot model performance comparison.
        
        Args:
            test_results: Dictionary with test results for each model
            save: Whether to save the figure
            
        Returns:
            Matplotlib figure
        """
        # Extract metrics
        models = list(test_results.keys())
        metrics = ['accuracy', 'precision', 'recall', 'f1', 'roc_auc']
        
        performance_data = []
        for model in models:
            model_metrics = test_results[model]['metrics']
            for metric in metrics:
                if metric in model_metrics:
                    performance_data.append({
                        'Model': model.replace('_', ' ').title(),
                        'Metric': metric.upper().replace('_', '-'),
                        'Score': model_metrics[metric]
                    })
        
        performance_df = pd.DataFrame(performance_data)
        
        # Create figure
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))
        
        # Bar plot
        sns.barplot(data=performance_df, x='Model', y='Score', hue='Metric', ax=ax1)
        ax1.set_title('Model Performance Comparison', fontweight='bold')
        ax1.set_ylabel('Score')
        ax1.set_ylim(0, 1)
        ax1.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        plt.setp(ax1.xaxis.get_majorticklabels(), rotation=45, ha='right')
        
        # Radar chart for best model
        best_model = max(models, key=lambda m: test_results[m]['metrics']['accuracy'])
        best_metrics = test_results[best_model]['metrics']
        
        angles = np.linspace(0, 2 * np.pi, len(metrics), endpoint=False).tolist()
        values = [best_metrics.get(metric, 0) for metric in metrics]
        
        # Close the plot
        angles += angles[:1]
        values += values[:1]
        
        ax2 = plt.subplot(122, projection='polar')
        ax2.plot(angles, values, 'o-', linewidth=2, label=best_model.replace('_', ' ').title())
        ax2.fill(angles, values, alpha=0.25)
        ax2.set_xticks(angles[:-1])
        ax2.set_xticklabels([m.upper().replace('_', '-') for m in metrics])
        ax2.set_ylim(0, 1)
        ax2.set_title(f'Best Model Performance\n({best_model.replace("_", " ").title()})', 
                     fontweight='bold', pad=20)
        ax2.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0))
        
        plt.tight_layout()
        
        if save:
            plt.savefig(os.path.join(self.figure_dir, 'model_performance.png'), 
                       dpi=300, bbox_inches='tight')
        
        return fig
    
    def plot_confusion_matrices(self, test_results: Dict[str, Dict], save: bool = True) -> plt.Figure:
        """
        Plot confusion matrices for all models.
        
        Args:
            test_results: Dictionary with test results for each model
            save: Whether to save the figure
            
        Returns:
            Matplotlib figure
        """
        n_models = len(test_results)
        cols = 2
        rows = (n_models + 1) // 2
        
        fig, axes = plt.subplots(rows, cols, figsize=(12, 6*rows))
        fig.suptitle('Confusion Matrices', fontsize=16, fontweight='bold')
        
        if n_models == 1:
            axes = [axes]
        elif rows == 1:
            axes = [axes]
        else:
            axes = axes.flatten()
        
        for i, (model_name, results) in enumerate(test_results.items()):
            ax = axes[i] if n_models > 1 else axes[0]
            
            cm = results['confusion_matrix']
            
            # Create heatmap
            sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', ax=ax,
                       xticklabels=['Non-Neurotoxic', 'Neurotoxic'],
                       yticklabels=['Non-Neurotoxic', 'Neurotoxic'])
            
            ax.set_title(f'{model_name.replace("_", " ").title()}')
            ax.set_xlabel('Predicted')
            ax.set_ylabel('Actual')
            
            # Add accuracy to title
            accuracy = results['metrics']['accuracy']
            ax.set_title(f'{model_name.replace("_", " ").title()}\nAccuracy: {accuracy:.3f}')
        
        # Hide unused subplots
        for i in range(n_models, len(axes)):
            axes[i].set_visible(False)
        
        plt.tight_layout()

        if save:
            plt.savefig(os.path.join(self.figure_dir, 'confusion_matrices.png'),
                       dpi=300, bbox_inches='tight')

        return fig

    def plot_roc_curves(self, test_results: Dict[str, Dict], y_test: pd.Series,
                       save: bool = True) -> plt.Figure:
        """
        Plot ROC curves for all models.

        Args:
            test_results: Dictionary with test results for each model
            y_test: True test labels
            save: Whether to save the figure

        Returns:
            Matplotlib figure
        """
        fig, ax = plt.subplots(figsize=(10, 8))

        colors = plt.cm.Set1(np.linspace(0, 1, len(test_results)))

        for i, (model_name, results) in enumerate(test_results.items()):
            if results['probabilities'] is not None:
                fpr, tpr, _ = roc_curve(y_test, results['probabilities'])
                roc_auc = auc(fpr, tpr)

                ax.plot(fpr, tpr, color=colors[i], lw=2,
                       label=f'{model_name.replace("_", " ").title()} (AUC = {roc_auc:.3f})')

        # Plot diagonal line
        ax.plot([0, 1], [0, 1], color='gray', lw=2, linestyle='--', alpha=0.8)

        ax.set_xlim([0.0, 1.0])
        ax.set_ylim([0.0, 1.05])
        ax.set_xlabel('False Positive Rate')
        ax.set_ylabel('True Positive Rate')
        ax.set_title('ROC Curves - Neurotoxicity Prediction', fontweight='bold')
        ax.legend(loc="lower right")
        ax.grid(True, alpha=0.3)

        if save:
            plt.savefig(os.path.join(self.figure_dir, 'roc_curves.png'),
                       dpi=300, bbox_inches='tight')

        return fig

    def create_summary_report(self, df: pd.DataFrame, test_results: Dict[str, Dict],
                             importance_data: Dict[str, pd.DataFrame]) -> None:
        """
        Create a comprehensive summary report with all visualizations.

        Args:
            df: Original dataset
            test_results: Test results for all models
            importance_data: Feature importance data for all models
        """
        print("Creating comprehensive visualization report...")

        # Create all plots
        self.plot_data_distribution(df)
        self.plot_correlation_matrix(df)
        self.plot_feature_importance(importance_data)
        self.plot_model_performance(test_results)
        self.plot_confusion_matrices(test_results)

        # Extract y_test for ROC curves
        y_test = pd.read_csv('data/train_test_split/y_test.csv').squeeze()
        self.plot_roc_curves(test_results, y_test)

        print(f"All visualizations saved to {self.figure_dir}")

        # Create summary statistics
        self._create_summary_stats(df, test_results, importance_data)

    def _create_summary_stats(self, df: pd.DataFrame, test_results: Dict[str, Dict],
                             importance_data: Dict[str, pd.DataFrame]) -> None:
        """Create and save summary statistics."""
        summary = {
            'Dataset Statistics': {
                'Total Compounds': len(df),
                'Neurotoxic Compounds': df['neurotoxic'].sum(),
                'Non-Neurotoxic Compounds': (1 - df['neurotoxic']).sum(),
                'Neurotoxic Percentage': f"{df['neurotoxic'].mean():.1%}",
                'Number of Features': len([col for col in df.columns if col not in ['compound_id', 'neurotoxic', 'neurotox_probability']])
            },
            'Best Model Performance': {},
            'Top 5 Important Features': {}
        }

        # Find best model
        best_model = max(test_results.keys(),
                        key=lambda m: test_results[m]['metrics']['accuracy'])

        summary['Best Model Performance'] = {
            'Model': best_model.replace('_', ' ').title(),
            'Accuracy': f"{test_results[best_model]['metrics']['accuracy']:.3f}",
            'Precision': f"{test_results[best_model]['metrics']['precision']:.3f}",
            'Recall': f"{test_results[best_model]['metrics']['recall']:.3f}",
            'F1-Score': f"{test_results[best_model]['metrics']['f1']:.3f}",
            'ROC-AUC': f"{test_results[best_model]['metrics'].get('roc_auc', 'N/A')}"
        }

        # Top features from best model
        if best_model in importance_data:
            top_features = importance_data[best_model].head(5)
            for i, (_, row) in enumerate(top_features.iterrows(), 1):
                summary['Top 5 Important Features'][f'{i}. {row["feature"]}'] = f"{row['importance']:.3f}"

        # Save summary
        summary_path = os.path.join(self.output_dir, 'summary_statistics.txt')
        with open(summary_path, 'w') as f:
            for section, data in summary.items():
                f.write(f"{section}:\n")
                f.write("-" * len(section) + "-\n")
                for key, value in data.items():
                    f.write(f"{key}: {value}\n")
                f.write("\n")

        print(f"Summary statistics saved to {summary_path}")


def main():
    """Create all visualizations for the neurotoxicity prediction project."""
    print("Creating visualizations for NeuroToxPredict project...")

    # Initialize visualizer
    visualizer = NeuroToxVisualizer()

    # Load data
    print("Loading data...")
    df = pd.read_csv('data/raw_molecular_data.csv')

    # Load test results (if available)
    test_results = {}
    importance_data = {}

    model_dir = 'results/model_performance'
    if os.path.exists(model_dir):
        # Load feature importance files
        for file in os.listdir(model_dir):
            if file.endswith('_feature_importance.csv'):
                model_name = file.replace('_feature_importance.csv', '')
                importance_data[model_name] = pd.read_csv(os.path.join(model_dir, file))

        print(f"Loaded feature importance for {len(importance_data)} models")

    # Create basic visualizations
    visualizer.plot_data_distribution(df)
    visualizer.plot_correlation_matrix(df)

    if importance_data:
        visualizer.plot_feature_importance(importance_data)

    print("Basic visualizations created!")
    print(f"Figures saved to: {visualizer.figure_dir}")


if __name__ == "__main__":
    main()
