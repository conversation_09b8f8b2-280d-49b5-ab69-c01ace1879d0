"""
Utility Functions for NeuroToxPredict Project

This module contains helper functions for data processing, model evaluation,
and project management.

Author: Intel ISEF Project - NeuroToxPredict
"""

import pandas as pd
import numpy as np
import os
import json
from typing import Dict, List, Any, Tuple
import time
from datetime import datetime


def setup_project_directories():
    """Create all necessary project directories."""
    directories = [
        'data',
        'data/train_test_split',
        'src',
        'notebooks',
        'results',
        'results/model_performance',
        'results/feature_importance',
        'results/predictions',
        'results/figures',
        'docs',
        'tests'
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
    
    print("Project directories created successfully!")


def log_experiment(experiment_name: str, parameters: Dict[str, Any], 
                  results: Dict[str, Any], log_file: str = 'results/experiment_log.json'):
    """
    Log experiment parameters and results.
    
    Args:
        experiment_name: Name of the experiment
        parameters: Dictionary of experiment parameters
        results: Dictionary of experiment results
        log_file: Path to log file
    """
    # Create log entry
    log_entry = {
        'timestamp': datetime.now().isoformat(),
        'experiment_name': experiment_name,
        'parameters': parameters,
        'results': results
    }
    
    # Load existing log or create new one
    if os.path.exists(log_file):
        with open(log_file, 'r') as f:
            log_data = json.load(f)
    else:
        log_data = []
    
    # Add new entry
    log_data.append(log_entry)
    
    # Save updated log
    with open(log_file, 'w') as f:
        json.dump(log_data, f, indent=2)
    
    print(f"Experiment '{experiment_name}' logged successfully!")


def calculate_model_statistics(y_true: np.ndarray, y_pred: np.ndarray, 
                              y_proba: np.ndarray = None) -> Dict[str, float]:
    """
    Calculate comprehensive model performance statistics.
    
    Args:
        y_true: True labels
        y_pred: Predicted labels
        y_proba: Predicted probabilities (optional)
        
    Returns:
        Dictionary with performance metrics
    """
    from sklearn.metrics import (
        accuracy_score, precision_score, recall_score, f1_score,
        roc_auc_score, matthews_corrcoef, balanced_accuracy_score
    )
    
    stats = {
        'accuracy': accuracy_score(y_true, y_pred),
        'balanced_accuracy': balanced_accuracy_score(y_true, y_pred),
        'precision': precision_score(y_true, y_pred, average='weighted'),
        'recall': recall_score(y_true, y_pred, average='weighted'),
        'f1_score': f1_score(y_true, y_pred, average='weighted'),
        'matthews_corrcoef': matthews_corrcoef(y_true, y_pred)
    }
    
    if y_proba is not None:
        stats['roc_auc'] = roc_auc_score(y_true, y_proba)
    
    return stats


def validate_dataset(df: pd.DataFrame) -> Dict[str, Any]:
    """
    Validate the generated dataset for quality and consistency.
    
    Args:
        df: Dataset DataFrame
        
    Returns:
        Dictionary with validation results
    """
    validation_results = {
        'total_samples': len(df),
        'missing_values': df.isnull().sum().sum(),
        'duplicate_rows': df.duplicated().sum(),
        'class_distribution': df['neurotoxic'].value_counts().to_dict(),
        'feature_ranges': {},
        'outliers': {},
        'data_quality_score': 0.0
    }
    
    # Check feature ranges
    numeric_columns = df.select_dtypes(include=[np.number]).columns
    for col in numeric_columns:
        if col not in ['compound_id', 'neurotoxic']:
            validation_results['feature_ranges'][col] = {
                'min': float(df[col].min()),
                'max': float(df[col].max()),
                'mean': float(df[col].mean()),
                'std': float(df[col].std())
            }
            
            # Detect outliers using IQR method
            Q1 = df[col].quantile(0.25)
            Q3 = df[col].quantile(0.75)
            IQR = Q3 - Q1
            outlier_count = ((df[col] < (Q1 - 1.5 * IQR)) | 
                           (df[col] > (Q3 + 1.5 * IQR))).sum()
            validation_results['outliers'][col] = int(outlier_count)
    
    # Calculate data quality score
    quality_factors = []
    
    # Factor 1: Missing values (lower is better)
    missing_ratio = validation_results['missing_values'] / (len(df) * len(df.columns))
    quality_factors.append(1 - missing_ratio)
    
    # Factor 2: Duplicate rows (lower is better)
    duplicate_ratio = validation_results['duplicate_rows'] / len(df)
    quality_factors.append(1 - duplicate_ratio)
    
    # Factor 3: Class balance (closer to 0.5 is better for binary classification)
    class_balance = min(df['neurotoxic'].mean(), 1 - df['neurotoxic'].mean())
    quality_factors.append(class_balance * 2)  # Scale to 0-1
    
    # Factor 4: Feature variability (higher is better)
    feature_variability = []
    for col in numeric_columns:
        if col not in ['compound_id', 'neurotoxic'] and df[col].std() > 0:
            cv = df[col].std() / df[col].mean() if df[col].mean() != 0 else 0
            feature_variability.append(min(cv, 1))  # Cap at 1
    
    if feature_variability:
        quality_factors.append(np.mean(feature_variability))
    
    validation_results['data_quality_score'] = np.mean(quality_factors)
    
    return validation_results


def create_prediction_report(model_name: str, predictions: np.ndarray, 
                           probabilities: np.ndarray, compound_ids: List[str],
                           confidence_threshold: float = 0.8) -> pd.DataFrame:
    """
    Create a detailed prediction report.
    
    Args:
        model_name: Name of the model
        predictions: Binary predictions
        probabilities: Prediction probabilities
        compound_ids: List of compound IDs
        confidence_threshold: Threshold for high-confidence predictions
        
    Returns:
        DataFrame with prediction report
    """
    report_df = pd.DataFrame({
        'compound_id': compound_ids,
        'predicted_class': predictions,
        'predicted_probability': probabilities,
        'prediction_confidence': np.maximum(probabilities, 1 - probabilities),
        'high_confidence': np.maximum(probabilities, 1 - probabilities) >= confidence_threshold
    })
    
    # Add risk categories
    def categorize_risk(prob):
        if prob >= 0.8:
            return 'High Risk'
        elif prob >= 0.6:
            return 'Moderate Risk'
        elif prob >= 0.4:
            return 'Low Risk'
        else:
            return 'Very Low Risk'
    
    report_df['risk_category'] = report_df['predicted_probability'].apply(categorize_risk)
    
    # Sort by probability (highest risk first)
    report_df = report_df.sort_values('predicted_probability', ascending=False)
    
    return report_df


def benchmark_models(models: Dict[str, Any], X_test: pd.DataFrame, 
                    y_test: pd.Series) -> pd.DataFrame:
    """
    Benchmark multiple models and create comparison table.
    
    Args:
        models: Dictionary of trained models
        X_test: Test features
        y_test: Test labels
        
    Returns:
        DataFrame with model comparison
    """
    benchmark_results = []
    
    for model_name, model in models.items():
        start_time = time.time()
        
        # Make predictions
        y_pred = model.predict(X_test)
        prediction_time = time.time() - start_time
        
        # Get probabilities if available
        y_proba = None
        if hasattr(model, 'predict_proba'):
            y_proba = model.predict_proba(X_test)[:, 1]
        
        # Calculate metrics
        metrics = calculate_model_statistics(y_test, y_pred, y_proba)
        
        # Add timing and model info
        result = {
            'Model': model_name.replace('_', ' ').title(),
            'Accuracy': metrics['accuracy'],
            'Precision': metrics['precision'],
            'Recall': metrics['recall'],
            'F1-Score': metrics['f1_score'],
            'ROC-AUC': metrics.get('roc_auc', np.nan),
            'MCC': metrics['matthews_corrcoef'],
            'Prediction_Time_ms': prediction_time * 1000,
            'Interpretability': get_interpretability_score(model_name)
        }
        
        benchmark_results.append(result)
    
    benchmark_df = pd.DataFrame(benchmark_results)
    benchmark_df = benchmark_df.sort_values('Accuracy', ascending=False)
    
    return benchmark_df


def get_interpretability_score(model_name: str) -> str:
    """
    Get interpretability score for different model types.
    
    Args:
        model_name: Name of the model
        
    Returns:
        Interpretability rating
    """
    interpretability_map = {
        'decision_tree': 'Very High',
        'logistic_regression': 'High',
        'random_forest': 'Medium',
        'shallow_nn': 'Medium-Low',
        'svm': 'Low',
        'deep_nn': 'Very Low'
    }
    
    return interpretability_map.get(model_name, 'Unknown')


def save_project_metadata():
    """Save project metadata and configuration."""
    metadata = {
        'project_name': 'NeuroToxPredict',
        'version': '1.0.0',
        'author': 'Intel ISEF Project',
        'description': 'AI-Driven Neurotoxicity Prediction Framework',
        'created_date': datetime.now().isoformat(),
        'python_version': '3.8+',
        'dependencies': [
            'numpy>=1.21.0',
            'pandas>=1.3.0',
            'scikit-learn>=1.0.0',
            'matplotlib>=3.4.0',
            'seaborn>=0.11.0'
        ],
        'dataset_info': {
            'synthetic_compounds': 10000,
            'features': 15,
            'target': 'neurotoxic (binary)',
            'class_distribution': 'approximately 30% positive'
        },
        'models': [
            'Decision Tree',
            'Random Forest',
            'Logistic Regression',
            'Shallow Neural Network'
        ]
    }
    
    with open('project_metadata.json', 'w') as f:
        json.dump(metadata, f, indent=2)
    
    print("Project metadata saved!")


if __name__ == "__main__":
    # Setup project when run directly
    setup_project_directories()
    save_project_metadata()
    print("Project utilities initialized!")
