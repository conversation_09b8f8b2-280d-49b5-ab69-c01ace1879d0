"""
Machine Learning Models for Neurotoxicity Prediction

This module implements interpretable machine learning models including decision trees,
random forests, and shallow neural networks for predicting neurotoxicity.

Author: Intel ISEF Project - NeuroToxPredict
"""

import numpy as np
import pandas as pd
from sklearn.tree import DecisionTreeClassifier
from sklearn.ensemble import RandomForestClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.neural_network import MLPClassifier
from sklearn.metrics import (
    accuracy_score, precision_score, recall_score, f1_score,
    roc_auc_score, confusion_matrix, classification_report
)
from sklearn.model_selection import cross_val_score, GridSearchCV
import joblib
import os
from typing import Dict, Tuple, Any
import warnings
warnings.filterwarnings('ignore')


class NeuroToxPredictor:
    """
    Ensemble of interpretable machine learning models for neurotoxicity prediction.
    """
    
    def __init__(self, random_state: int = 42):
        """Initialize the predictor with multiple interpretable models."""
        self.random_state = random_state
        self.models = {}
        self.model_performance = {}
        self.feature_importance = {}
        
        # Initialize interpretable models
        self._initialize_models()
    
    def _initialize_models(self):
        """Initialize all machine learning models."""
        
        # Decision Tree - Highly interpretable
        self.models['decision_tree'] = DecisionTreeClassifier(
            max_depth=10,
            min_samples_split=20,
            min_samples_leaf=10,
            random_state=self.random_state,
            class_weight='balanced'
        )
        
        # Random Forest - Interpretable with feature importance
        self.models['random_forest'] = RandomForestClassifier(
            n_estimators=100,
            max_depth=15,
            min_samples_split=10,
            min_samples_leaf=5,
            random_state=self.random_state,
            class_weight='balanced'
        )
        
        # Logistic Regression - Linear and interpretable
        self.models['logistic_regression'] = LogisticRegression(
            random_state=self.random_state,
            class_weight='balanced',
            max_iter=1000
        )
        
        # Shallow Neural Network - 2 hidden layers for interpretability
        self.models['shallow_nn'] = MLPClassifier(
            hidden_layer_sizes=(50, 25),
            activation='relu',
            solver='adam',
            alpha=0.001,
            random_state=self.random_state,
            max_iter=500,
            early_stopping=True,
            validation_fraction=0.1
        )
    
    def train_models(self, X_train: pd.DataFrame, y_train: pd.Series,
                    X_val: pd.DataFrame = None, y_val: pd.Series = None) -> Dict[str, Any]:
        """
        Train all models and evaluate performance.
        
        Args:
            X_train: Training features
            y_train: Training labels
            X_val: Validation features (optional)
            y_val: Validation labels (optional)
            
        Returns:
            Dictionary with training results
        """
        print("Training neurotoxicity prediction models...")
        
        training_results = {}
        
        for model_name, model in self.models.items():
            print(f"\nTraining {model_name}...")
            
            # Train the model
            model.fit(X_train, y_train)
            
            # Make predictions
            y_train_pred = model.predict(X_train)
            y_train_proba = model.predict_proba(X_train)[:, 1] if hasattr(model, 'predict_proba') else None
            
            # Calculate training metrics
            train_metrics = self._calculate_metrics(y_train, y_train_pred, y_train_proba)
            
            # Validation metrics if validation set provided
            val_metrics = {}
            if X_val is not None and y_val is not None:
                y_val_pred = model.predict(X_val)
                y_val_proba = model.predict_proba(X_val)[:, 1] if hasattr(model, 'predict_proba') else None
                val_metrics = self._calculate_metrics(y_val, y_val_pred, y_val_proba)
            
            # Cross-validation score
            cv_scores = cross_val_score(model, X_train, y_train, cv=5, scoring='roc_auc')
            
            # Store results
            training_results[model_name] = {
                'train_metrics': train_metrics,
                'val_metrics': val_metrics,
                'cv_mean': cv_scores.mean(),
                'cv_std': cv_scores.std()
            }
            
            # Extract feature importance
            self._extract_feature_importance(model_name, model, X_train.columns)
            
            print(f"Training accuracy: {train_metrics['accuracy']:.3f}")
            if val_metrics:
                print(f"Validation accuracy: {val_metrics['accuracy']:.3f}")
            print(f"CV ROC-AUC: {cv_scores.mean():.3f} ± {cv_scores.std():.3f}")
        
        self.model_performance = training_results
        return training_results
    
    def _calculate_metrics(self, y_true: pd.Series, y_pred: np.ndarray, 
                          y_proba: np.ndarray = None) -> Dict[str, float]:
        """Calculate comprehensive evaluation metrics."""
        metrics = {
            'accuracy': accuracy_score(y_true, y_pred),
            'precision': precision_score(y_true, y_pred, average='weighted'),
            'recall': recall_score(y_true, y_pred, average='weighted'),
            'f1': f1_score(y_true, y_pred, average='weighted')
        }
        
        if y_proba is not None:
            metrics['roc_auc'] = roc_auc_score(y_true, y_proba)
        
        return metrics
    
    def _extract_feature_importance(self, model_name: str, model: Any, feature_names: pd.Index):
        """Extract feature importance from trained models."""
        importance = None
        
        if hasattr(model, 'feature_importances_'):
            # Tree-based models
            importance = model.feature_importances_
        elif hasattr(model, 'coef_'):
            # Linear models
            importance = np.abs(model.coef_[0])
        
        if importance is not None:
            self.feature_importance[model_name] = pd.DataFrame({
                'feature': feature_names,
                'importance': importance
            }).sort_values('importance', ascending=False)
    
    def hyperparameter_tuning(self, X_train: pd.DataFrame, y_train: pd.Series,
                             model_name: str = 'random_forest') -> Dict[str, Any]:
        """
        Perform hyperparameter tuning for a specific model.
        
        Args:
            X_train: Training features
            y_train: Training labels
            model_name: Name of model to tune
            
        Returns:
            Best parameters and performance
        """
        print(f"Performing hyperparameter tuning for {model_name}...")
        
        param_grids = {
            'decision_tree': {
                'max_depth': [5, 10, 15, 20],
                'min_samples_split': [10, 20, 50],
                'min_samples_leaf': [5, 10, 20]
            },
            'random_forest': {
                'n_estimators': [50, 100, 200],
                'max_depth': [10, 15, 20],
                'min_samples_split': [5, 10, 20],
                'min_samples_leaf': [2, 5, 10]
            },
            'logistic_regression': {
                'C': [0.1, 1.0, 10.0],
                'penalty': ['l1', 'l2'],
                'solver': ['liblinear', 'saga']
            }
        }
        
        if model_name not in param_grids:
            print(f"No parameter grid defined for {model_name}")
            return {}
        
        # Get base model
        base_model = self.models[model_name]
        
        # Grid search
        grid_search = GridSearchCV(
            base_model, param_grids[model_name],
            cv=5, scoring='roc_auc', n_jobs=-1
        )
        
        grid_search.fit(X_train, y_train)
        
        # Update model with best parameters
        self.models[model_name] = grid_search.best_estimator_
        
        return {
            'best_params': grid_search.best_params_,
            'best_score': grid_search.best_score_,
            'cv_results': grid_search.cv_results_
        }
    
    def evaluate_test_set(self, X_test: pd.DataFrame, y_test: pd.Series) -> Dict[str, Dict]:
        """
        Evaluate all trained models on the test set.
        
        Args:
            X_test: Test features
            y_test: Test labels
            
        Returns:
            Test performance for all models
        """
        print("Evaluating models on test set...")
        
        test_results = {}
        
        for model_name, model in self.models.items():
            # Predictions
            y_pred = model.predict(X_test)
            y_proba = model.predict_proba(X_test)[:, 1] if hasattr(model, 'predict_proba') else None
            
            # Metrics
            metrics = self._calculate_metrics(y_test, y_pred, y_proba)
            
            # Confusion matrix
            cm = confusion_matrix(y_test, y_pred)
            
            # Classification report
            class_report = classification_report(y_test, y_pred, output_dict=True)
            
            test_results[model_name] = {
                'metrics': metrics,
                'confusion_matrix': cm,
                'classification_report': class_report,
                'predictions': y_pred,
                'probabilities': y_proba
            }
            
            print(f"\n{model_name} Test Results:")
            print(f"Accuracy: {metrics['accuracy']:.3f}")
            print(f"Precision: {metrics['precision']:.3f}")
            print(f"Recall: {metrics['recall']:.3f}")
            print(f"F1-Score: {metrics['f1']:.3f}")
            if 'roc_auc' in metrics:
                print(f"ROC-AUC: {metrics['roc_auc']:.3f}")
        
        return test_results
    
    def get_model_interpretability(self, model_name: str = 'decision_tree') -> Dict[str, Any]:
        """
        Extract interpretability information from a model.
        
        Args:
            model_name: Name of the model to interpret
            
        Returns:
            Interpretability information
        """
        if model_name not in self.models:
            raise ValueError(f"Model {model_name} not found")
        
        model = self.models[model_name]
        interpretation = {}
        
        # Feature importance
        if model_name in self.feature_importance:
            interpretation['feature_importance'] = self.feature_importance[model_name]
        
        # Decision tree specific
        if model_name == 'decision_tree':
            interpretation['tree_depth'] = model.tree_.max_depth
            interpretation['n_leaves'] = model.tree_.n_leaves
            interpretation['n_nodes'] = model.tree_.node_count
        
        # Linear model coefficients
        if hasattr(model, 'coef_'):
            interpretation['coefficients'] = dict(zip(
                self.feature_importance[model_name]['feature'],
                model.coef_[0]
            ))
        
        return interpretation
    
    def save_models(self, output_dir: str = 'results/model_performance'):
        """Save trained models and results."""
        os.makedirs(output_dir, exist_ok=True)
        
        # Save models
        for model_name, model in self.models.items():
            model_path = os.path.join(output_dir, f'{model_name}_model.joblib')
            joblib.dump(model, model_path)
        
        # Save feature importance
        for model_name, importance_df in self.feature_importance.items():
            importance_path = os.path.join(output_dir, f'{model_name}_feature_importance.csv')
            importance_df.to_csv(importance_path, index=False)
        
        print(f"Models and results saved to {output_dir}")


def main():
    """Train and evaluate neurotoxicity prediction models."""
    print("Starting model training pipeline...")

    # Check if processed data exists
    if not os.path.exists('data/train_test_split/X_train.csv'):
        print("Processed data not found. Running feature engineering first...")
        from feature_engineering import main as fe_main
        fe_main()

    print("Loading processed data...")

    # Load processed data
    X_train = pd.read_csv('data/train_test_split/X_train.csv')
    y_train = pd.read_csv('data/train_test_split/y_train.csv').squeeze()
    X_val = pd.read_csv('data/train_test_split/X_val.csv')
    y_val = pd.read_csv('data/train_test_split/y_val.csv').squeeze()
    X_test = pd.read_csv('data/train_test_split/X_test.csv')
    y_test = pd.read_csv('data/train_test_split/y_test.csv').squeeze()

    # Initialize predictor
    predictor = NeuroToxPredictor()

    # Train models
    training_results = predictor.train_models(X_train, y_train, X_val, y_val)

    # Hyperparameter tuning for best model
    tuning_results = predictor.hyperparameter_tuning(X_train, y_train, 'random_forest')

    # Evaluate on test set
    test_results = predictor.evaluate_test_set(X_test, y_test)

    # Save models and results
    predictor.save_models()

    # Print final summary
    print("\n" + "="*50)
    print("FINAL MODEL PERFORMANCE SUMMARY")
    print("="*50)

    for model_name in predictor.models.keys():
        test_acc = test_results[model_name]['metrics']['accuracy']
        test_auc = test_results[model_name]['metrics'].get('roc_auc', 'N/A')
        print(f"{model_name:20s}: Accuracy={test_acc:.3f}, ROC-AUC={test_auc}")

    print("\nModel training and evaluation complete!")


if __name__ == "__main__":
    main()
