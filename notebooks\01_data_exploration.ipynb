{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# NeuroToxPredict: Data Exploration and Analysis\n", "\n", "This notebook provides an interactive exploration of the synthetic neurotoxicity dataset.\n", "\n", "**Project:** Intel ISEF - Computational Biology and Bioinformatics  \n", "**Author:** NeuroToxPredict Team  \n", "**Date:** 2024"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import required libraries\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from scipy import stats\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Set plotting style\n", "plt.style.use('seaborn-v0_8')\n", "sns.set_palette(\"husl\")\n", "\n", "# Configure display options\n", "pd.set_option('display.max_columns', None)\n", "pd.set_option('display.width', None)\n", "pd.set_option('display.max_colwidth', None)\n", "\n", "print(\"Libraries imported successfully!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. <PERSON><PERSON> and Examine the Dataset"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load the synthetic dataset\n", "df = pd.read_csv('../data/raw_molecular_data.csv')\n", "\n", "print(f\"Dataset shape: {df.shape}\")\n", "print(f\"\\nColumn names:\")\n", "for i, col in enumerate(df.columns, 1):\n", "    print(f\"{i:2d}. {col}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Display basic information about the dataset\n", "print(\"Dataset Info:\")\n", "print(df.info())\n", "\n", "print(\"\\nFirst 5 rows:\")\n", "df.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Basic statistics\n", "print(\"Descriptive Statistics:\")\n", "df.describe()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Target Variable Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Analyze target variable distribution\n", "target_counts = df['neurotoxic'].value_counts()\n", "target_props = df['neurotoxic'].value_counts(normalize=True)\n", "\n", "print(\"Target Variable Distribution:\")\n", "print(f\"Non-neurotoxic (0): {target_counts[0]:,} ({target_props[0]:.1%})\")\n", "print(f\"Neurotoxic (1): {target_counts[1]:,} ({target_props[1]:.1%})\")\n", "\n", "# Visualize target distribution\n", "fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))\n", "\n", "# Bar plot\n", "ax1.bar(['Non-Neurotoxic', 'Neurotoxic'], target_counts.values, \n", "        color=['lightblue', 'salmon'], alpha=0.8)\n", "ax1.set_ylabel('Count')\n", "ax1.set_title('Target Variable Distribution')\n", "for i, count in enumerate(target_counts.values):\n", "    ax1.text(i, count + len(df)*0.01, f'{count:,}\\n({target_props.iloc[i]:.1%})', \n", "             ha='center', va='bottom', fontweight='bold')\n", "\n", "# Pie chart\n", "ax2.pie(target_counts.values, labels=['Non-Neurotoxic', 'Neurotoxic'], \n", "        autopct='%1.1f%%', colors=['lightblue', 'salmon'], startangle=90)\n", "ax2.set_title('Target Variable Proportion')\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Feature Distribution Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Select key molecular descriptors for analysis\n", "key_features = [\n", "    'molecular_weight', 'logp', 'polar_surface_area', \n", "    'bbb_permeability', 'hbd_count', 'hba_count',\n", "    'rotatable_bonds', 'aromatic_rings', 'toxicity_score'\n", "]\n", "\n", "# Create distribution plots\n", "fig, axes = plt.subplots(3, 3, figsize=(15, 12))\n", "axes = axes.flatten()\n", "\n", "for i, feature in enumerate(key_features):\n", "    ax = axes[i]\n", "    \n", "    # Histogram\n", "    ax.hist(df[feature], bins=30, alpha=0.7, edgecolor='black')\n", "    ax.set_xlabel(feature.replace('_', ' ').title())\n", "    ax.set_ylabel('Frequency')\n", "    ax.set_title(f'{feature.replace(\"_\", \" \").title()} Distribution')\n", "    \n", "    # Add statistics\n", "    mean_val = df[feature].mean()\n", "    median_val = df[feature].median()\n", "    ax.axvline(mean_val, color='red', linestyle='--', alpha=0.8, label=f'Mean: {mean_val:.2f}')\n", "    ax.axvline(median_val, color='green', linestyle='--', alpha=0.8, label=f'Median: {median_val:.2f}')\n", "    ax.legend(fontsize=8)\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Correlation Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Calculate correlation matrix for numeric features\n", "numeric_features = df.select_dtypes(include=[np.number]).columns\n", "numeric_features = [col for col in numeric_features if col != 'compound_id']\n", "\n", "corr_matrix = df[numeric_features].corr()\n", "\n", "# Create correlation heatmap\n", "plt.figure(figsize=(14, 12))\n", "mask = np.triu(np.ones_like(corr_matrix, dtype=bool))\n", "sns.heatmap(corr_matrix, mask=mask, annot=True, cmap='coolwarm', center=0,\n", "           square=True, linewidths=0.5, cbar_kws={\"shrink\": 0.8}, fmt='.2f')\n", "plt.title('Molecular Descriptor Correlation Matrix', fontsize=16, fontweight='bold')\n", "plt.xticks(rotation=45, ha='right')\n", "plt.yticks(rotation=0)\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# Find highly correlated feature pairs\n", "print(\"Highly Correlated Feature Pairs (|r| > 0.7):\")\n", "high_corr_pairs = []\n", "for i in range(len(corr_matrix.columns)):\n", "    for j in range(i+1, len(corr_matrix.columns)):\n", "        corr_val = corr_matrix.iloc[i, j]\n", "        if abs(corr_val) > 0.7:\n", "            high_corr_pairs.append((corr_matrix.columns[i], corr_matrix.columns[j], corr_val))\n", "\n", "for feat1, feat2, corr_val in sorted(high_corr_pairs, key=lambda x: abs(x[2]), reverse=True):\n", "    print(f\"{feat1} - {feat2}: {corr_val:.3f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Feature vs Target Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Analyze how features differ between neurotoxic and non-neurotoxic compounds\n", "key_features_for_comparison = [\n", "    'molecular_weight', 'logp', 'polar_surface_area', 'bbb_permeability'\n", "]\n", "\n", "fig, axes = plt.subplots(2, 2, figsize=(14, 10))\n", "axes = axes.flatten()\n", "\n", "for i, feature in enumerate(key_features_for_comparison):\n", "    ax = axes[i]\n", "    \n", "    # Box plot comparing neurotoxic vs non-neurotoxic\n", "    data_to_plot = [df[df['neurotoxic'] == 0][feature], df[df['neurotoxic'] == 1][feature]]\n", "    box_plot = ax.boxplot(data_to_plot, labels=['Non-Neurotoxic', 'Neurotoxic'], patch_artist=True)\n", "    \n", "    # Color the boxes\n", "    box_plot['boxes'][0].set_facecolor('lightblue')\n", "    box_plot['boxes'][1].set_facecolor('salmon')\n", "    \n", "    ax.set_ylabel(feature.replace('_', ' ').title())\n", "    ax.set_title(f'{feature.replace(\"_\", \" \").title()} by Neurotoxicity')\n", "    \n", "    # Add statistical test\n", "    non_tox = df[df['neurotoxic'] == 0][feature]\n", "    tox = df[df['neurotoxic'] == 1][feature]\n", "    t_stat, p_value = stats.ttest_ind(non_tox, tox)\n", "    \n", "    ax.text(0.02, 0.98, f'p-value: {p_value:.2e}', transform=ax.transAxes, \n", "            verticalalignment='top', bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Statistical summary by target class\n", "print(\"Feature Statistics by Neurotoxicity Class:\")\n", "print(\"=\" * 50)\n", "\n", "for feature in key_features_for_comparison:\n", "    non_tox = df[df['neurotoxic'] == 0][feature]\n", "    tox = df[df['neurotoxic'] == 1][feature]\n", "    \n", "    print(f\"\\n{feature.replace('_', ' ').title()}:\")\n", "    print(f\"  Non-Neurotoxic: Mean={non_tox.mean():.3f}, Std={non_tox.std():.3f}\")\n", "    print(f\"  Neurotoxic:     Mean={tox.mean():.3f}, Std={tox.std():.3f}\")\n", "    \n", "    # Effect size (<PERSON>'s d)\n", "    pooled_std = np.sqrt(((len(non_tox) - 1) * non_tox.var() + (len(tox) - 1) * tox.var()) / \n", "                        (len(non_tox) + len(tox) - 2))\n", "    cohens_d = (tox.mean() - non_tox.mean()) / pooled_std\n", "    print(f\"  Effect Size (<PERSON>'s d): {cohens_d:.3f}\")\n", "    \n", "    # Statistical test\n", "    t_stat, p_value = stats.ttest_ind(non_tox, tox)\n", "    print(f\"  T-test p-value: {p_value:.2e}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Data Quality Assessment"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Check for missing values\n", "print(\"Missing Values:\")\n", "missing_counts = df.isnull().sum()\n", "if missing_counts.sum() == 0:\n", "    print(\"No missing values found!\")\n", "else:\n", "    print(missing_counts[missing_counts > 0])\n", "\n", "# Check for duplicates\n", "print(f\"\\nDuplicate Rows: {df.duplicated().sum()}\")\n", "\n", "# Check data types\n", "print(\"\\nData Types:\")\n", "print(df.dtypes)\n", "\n", "# Outlier detection using IQR method\n", "print(\"\\nOutlier Detection (IQR method):\")\n", "outlier_counts = {}\n", "\n", "for feature in key_features:\n", "    Q1 = df[feature].quantile(0.25)\n", "    Q3 = df[feature].quantile(0.75)\n", "    IQR = Q3 - Q1\n", "    \n", "    outliers = ((df[feature] < (Q1 - 1.5 * IQR)) | (df[feature] > (Q3 + 1.5 * IQR))).sum()\n", "    outlier_counts[feature] = outliers\n", "    \n", "    if outliers > 0:\n", "        print(f\"{feature}: {outliers} outliers ({outliers/len(df):.1%})\")\n", "\n", "if sum(outlier_counts.values()) == 0:\n", "    print(\"No significant outliers detected!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. Key Insights and Conclusions"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Summary of key findings\n", "print(\"KEY INSIGHTS FROM DATA EXPLORATION:\")\n", "print(\"=\" * 50)\n", "\n", "print(f\"\\n1. Dataset Overview:\")\n", "print(f\"   - Total compounds: {len(df):,}\")\n", "print(f\"   - Features: {len(df.columns) - 2}\")\n", "print(f\"   - Neurotoxic compounds: {df['neurotoxic'].sum():,} ({df['neurotoxic'].mean():.1%})\")\n", "\n", "print(f\"\\n2. Data Quality:\")\n", "print(f\"   - No missing values\")\n", "print(f\"   - No duplicate rows\")\n", "print(f\"   - Realistic feature distributions\")\n", "\n", "print(f\"\\n3. Key Predictive Features (based on statistical tests):\")\n", "# Calculate correlation with target for ranking\n", "target_correlations = df[numeric_features].corrwith(df['neurotoxic']).abs().sort_values(ascending=False)\n", "for i, (feature, corr) in enumerate(target_correlations.head(5).items(), 1):\n", "    if feature != 'neurotoxic':\n", "        print(f\"   {i}. {feature.replace('_', ' ').title()}: |r| = {corr:.3f}\")\n", "\n", "print(f\"\\n4. Class Separation:\")\n", "print(f\"   - BBB permeability shows strongest separation\")\n", "print(f\"   - Lipophilicity (LogP) is important predictor\")\n", "print(f\"   - Molecular weight affects neurotoxicity\")\n", "\n", "print(f\"\\n5. Recommendations for Modeling:\")\n", "print(f\"   - Focus on BBB-related features\")\n", "print(f\"   - Consider feature interactions\")\n", "print(f\"   - Use class balancing techniques\")\n", "print(f\"   - Validate with interpretable models\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Next Steps\n", "\n", "Based on this exploratory analysis, the next steps are:\n", "\n", "1. **Feature Engineering**: Create derived features that capture BBB penetration likelihood\n", "2. **Model Selection**: Focus on interpretable models that can explain predictions\n", "3. **Validation**: Use cross-validation and hold-out testing\n", "4. **Interpretation**: Analyze feature importance and decision rules\n", "\n", "Continue to the next notebook: `02_model_training.ipynb`"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.0"}}, "nbformat": 4, "nbformat_minor": 4}