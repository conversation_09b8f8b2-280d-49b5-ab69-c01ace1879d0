#!/usr/bin/env python3
"""
NeuroToxPredict: Main Execution Script

This script runs the complete neurotoxicity prediction pipeline from data generation
to model training and evaluation.

Author: Intel ISEF Project - NeuroToxPredict
Usage: python main.py [--skip-data-generation] [--skip-training] [--quick-run]
"""

import argparse
import sys
import os
import time
from datetime import datetime

# Add src directory to path
sys.path.append('src')

from data_generation import NeuroToxDataGenerator
from feature_engineering import FeatureEngineer
from models import NeuroToxPredictor
from visualization import NeuroToxVisualizer
from utils import setup_project_directories, log_experiment, save_project_metadata


def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='NeuroToxPredict: AI-Driven Neurotoxicity Prediction')
    
    parser.add_argument('--skip-data-generation', action='store_true',
                       help='Skip data generation step (use existing data)')
    parser.add_argument('--skip-training', action='store_true',
                       help='Skip model training step (use existing models)')
    parser.add_argument('--quick-run', action='store_true',
                       help='Run with reduced dataset size for quick testing')
    parser.add_argument('--n-compounds', type=int, default=10000,
                       help='Number of compounds to generate (default: 10000)')
    parser.add_argument('--random-seed', type=int, default=42,
                       help='Random seed for reproducibility (default: 42)')
    parser.add_argument('--output-dir', type=str, default='results',
                       help='Output directory for results (default: results)')
    
    return parser.parse_args()


def run_data_generation(args):
    """Run the data generation pipeline."""
    print("\n" + "="*60)
    print("STEP 1: SYNTHETIC DATA GENERATION")
    print("="*60)
    
    start_time = time.time()
    
    # Adjust sample size for quick run
    n_compounds = 1000 if args.quick_run else args.n_compounds
    
    print(f"Generating {n_compounds} synthetic compounds...")
    
    # Initialize generator
    generator = NeuroToxDataGenerator(random_seed=args.random_seed)
    
    # Generate molecular descriptors
    print("Creating molecular descriptors...")
    descriptors = generator.generate_molecular_descriptors(n_compounds=n_compounds)
    
    # Generate neurotoxicity labels
    print("Generating neurotoxicity labels...")
    full_dataset = generator.generate_neurotoxicity_labels(descriptors)
    
    # Save dataset
    full_dataset.to_csv('data/raw_molecular_data.csv', index=False)
    
    # Print summary
    generation_time = time.time() - start_time
    print(f"\nData generation completed in {generation_time:.2f} seconds")
    print(f"Dataset saved: {len(full_dataset)} compounds")
    print(f"Neurotoxic compounds: {full_dataset['neurotoxic'].sum()} ({full_dataset['neurotoxic'].mean():.1%})")
    
    return full_dataset


def run_feature_engineering(args):
    """Run the feature engineering pipeline."""
    print("\n" + "="*60)
    print("STEP 2: FEATURE ENGINEERING")
    print("="*60)
    
    start_time = time.time()
    
    # Initialize feature engineer
    fe = FeatureEngineer()
    
    # Load data
    print("Loading raw data...")
    df = fe.load_data('data/raw_molecular_data.csv')
    
    # Prepare features
    print("Engineering features...")
    X, y = fe.prepare_features(df, scale_features=True, select_features=False)
    
    # Split data
    print("Splitting data into train/validation/test sets...")
    data_splits = fe.split_data(X, y, random_state=args.random_seed)
    
    # Calculate feature importance
    print("Calculating feature importance...")
    importance_df = fe.get_feature_importance_scores(X, y)
    
    # Save processed data
    fe.save_processed_data(data_splits)
    importance_df.to_csv('data/feature_importance.csv', index=False)
    
    # Print summary
    engineering_time = time.time() - start_time
    print(f"\nFeature engineering completed in {engineering_time:.2f} seconds")
    print(f"Features created: {len(X.columns)}")
    print(f"Training samples: {len(data_splits['X_train'])}")
    print(f"Validation samples: {len(data_splits['X_val'])}")
    print(f"Test samples: {len(data_splits['X_test'])}")
    
    print(f"\nTop 5 most important features:")
    for i, row in importance_df.head().iterrows():
        print(f"  {row['feature']}: {row['combined_importance']:.3f}")
    
    return data_splits, importance_df


def run_model_training(args, data_splits):
    """Run the model training pipeline."""
    print("\n" + "="*60)
    print("STEP 3: MODEL TRAINING AND EVALUATION")
    print("="*60)
    
    start_time = time.time()
    
    # Initialize predictor
    predictor = NeuroToxPredictor(random_state=args.random_seed)
    
    # Train models
    print("Training machine learning models...")
    training_results = predictor.train_models(
        data_splits['X_train'], data_splits['y_train'],
        data_splits['X_val'], data_splits['y_val']
    )
    
    # Hyperparameter tuning for best model
    print("\nPerforming hyperparameter tuning...")
    tuning_results = predictor.hyperparameter_tuning(
        data_splits['X_train'], data_splits['y_train'], 'random_forest'
    )
    
    # Evaluate on test set
    print("\nEvaluating models on test set...")
    test_results = predictor.evaluate_test_set(
        data_splits['X_test'], data_splits['y_test']
    )
    
    # Save models and results
    predictor.save_models(args.output_dir + '/model_performance')
    
    # Print final summary
    training_time = time.time() - start_time
    print(f"\nModel training completed in {training_time:.2f} seconds")
    print("\nFinal Model Performance Summary:")
    print("-" * 50)
    
    for model_name in predictor.models.keys():
        test_acc = test_results[model_name]['metrics']['accuracy']
        test_auc = test_results[model_name]['metrics'].get('roc_auc', 'N/A')
        print(f"{model_name:20s}: Accuracy={test_acc:.3f}, ROC-AUC={test_auc}")
    
    # Log experiment
    experiment_params = {
        'n_compounds': len(data_splits['X_train']) + len(data_splits['X_val']) + len(data_splits['X_test']),
        'n_features': len(data_splits['X_train'].columns),
        'random_seed': args.random_seed,
        'quick_run': args.quick_run
    }
    
    best_model = max(test_results.keys(), key=lambda m: test_results[m]['metrics']['accuracy'])
    experiment_results = {
        'best_model': best_model,
        'best_accuracy': test_results[best_model]['metrics']['accuracy'],
        'best_roc_auc': test_results[best_model]['metrics'].get('roc_auc', None),
        'training_time_seconds': training_time
    }
    
    log_experiment('neurotox_prediction', experiment_params, experiment_results)
    
    return predictor, test_results


def run_visualization(args, test_results=None):
    """Run the visualization pipeline."""
    print("\n" + "="*60)
    print("STEP 4: VISUALIZATION AND ANALYSIS")
    print("="*60)
    
    start_time = time.time()
    
    # Initialize visualizer
    visualizer = NeuroToxVisualizer(output_dir=args.output_dir)
    
    # Load data
    print("Loading data for visualization...")
    df = pd.read_csv('data/raw_molecular_data.csv')
    
    # Load feature importance data
    importance_data = {}
    model_dir = os.path.join(args.output_dir, 'model_performance')
    
    if os.path.exists(model_dir):
        for file in os.listdir(model_dir):
            if file.endswith('_feature_importance.csv'):
                model_name = file.replace('_feature_importance.csv', '')
                importance_data[model_name] = pd.read_csv(os.path.join(model_dir, file))
    
    # Create visualizations
    print("Creating data distribution plots...")
    visualizer.plot_data_distribution(df)
    
    print("Creating correlation matrix...")
    visualizer.plot_correlation_matrix(df)
    
    if importance_data:
        print("Creating feature importance plots...")
        visualizer.plot_feature_importance(importance_data)
    
    if test_results:
        print("Creating model performance plots...")
        visualizer.plot_model_performance(test_results)
        visualizer.plot_confusion_matrices(test_results)
        
        # Load test labels for ROC curves
        y_test = pd.read_csv('data/train_test_split/y_test.csv').squeeze()
        visualizer.plot_roc_curves(test_results, y_test)
        
        # Create comprehensive summary
        visualizer.create_summary_report(df, test_results, importance_data)
    
    visualization_time = time.time() - start_time
    print(f"\nVisualization completed in {visualization_time:.2f} seconds")
    print(f"Figures saved to: {visualizer.figure_dir}")


def main():
    """Main execution function."""
    print("NeuroToxPredict: AI-Driven Neurotoxicity Prediction")
    print("Intel ISEF Project - Computational Biology and Bioinformatics")
    print("=" * 70)
    
    # Parse arguments
    args = parse_arguments()
    
    # Setup project
    print("Setting up project directories...")
    setup_project_directories()
    save_project_metadata()
    
    # Import pandas here to avoid import issues
    import pandas as pd
    
    # Track total execution time
    total_start_time = time.time()
    
    try:
        # Step 1: Data Generation
        if not args.skip_data_generation or not os.path.exists('data/raw_molecular_data.csv'):
            dataset = run_data_generation(args)
        else:
            print("\nSkipping data generation (using existing data)")
            dataset = pd.read_csv('data/raw_molecular_data.csv')
        
        # Step 2: Feature Engineering
        if not os.path.exists('data/train_test_split/X_train.csv'):
            data_splits, importance_df = run_feature_engineering(args)
        else:
            print("\nUsing existing processed data")
            data_splits = {
                'X_train': pd.read_csv('data/train_test_split/X_train.csv'),
                'y_train': pd.read_csv('data/train_test_split/y_train.csv').squeeze(),
                'X_val': pd.read_csv('data/train_test_split/X_val.csv'),
                'y_val': pd.read_csv('data/train_test_split/y_val.csv').squeeze(),
                'X_test': pd.read_csv('data/train_test_split/X_test.csv'),
                'y_test': pd.read_csv('data/train_test_split/y_test.csv').squeeze()
            }
        
        # Step 3: Model Training
        test_results = None
        if not args.skip_training:
            predictor, test_results = run_model_training(args, data_splits)
        else:
            print("\nSkipping model training (using existing models)")
        
        # Step 4: Visualization
        run_visualization(args, test_results)
        
        # Final summary
        total_time = time.time() - total_start_time
        print("\n" + "="*70)
        print("PIPELINE EXECUTION COMPLETED SUCCESSFULLY!")
        print("="*70)
        print(f"Total execution time: {total_time:.2f} seconds")
        print(f"Results saved to: {args.output_dir}")
        print("\nNext steps:")
        print("1. Review visualizations in results/figures/")
        print("2. Examine model performance in results/model_performance/")
        print("3. Check experiment log in results/experiment_log.json")
        print("4. Run Jupyter notebooks for interactive analysis")
        
    except Exception as e:
        print(f"\nError during execution: {str(e)}")
        print("Check the error message and try again.")
        sys.exit(1)


if __name__ == "__main__":
    main()
