"""
Feature Engineering Module for Neurotoxicity Prediction

This module handles data preprocessing, feature scaling, and train/test splitting
for the neurotoxicity prediction models.

Author: Intel ISEF Project - NeuroToxPredict
"""

import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler, MinMaxScaler
from sklearn.feature_selection import SelectKBest, f_classif, mutual_info_classif
from typing import Tuple, List, Dict
import os


class FeatureEngineer:
    """
    Handles feature engineering and preprocessing for neurotoxicity prediction.
    """
    
    def __init__(self):
        """Initialize the feature engineer."""
        self.scaler = None
        self.feature_selector = None
        self.feature_names = None
        self.target_column = 'neurotoxic'
        
        # Define feature groups for analysis
        self.feature_groups = {
            'physicochemical': [
                'molecular_weight', 'logp', 'polar_surface_area', 
                'formal_charge', 'complexity'
            ],
            'structural': [
                'hbd_count', 'hba_count', 'rotatable_bonds', 'aromatic_rings'
            ],
            'pharmacokinetic': [
                'bbb_permeability', 'protein_binding', 'half_life', 'bioavailability'
            ],
            'toxicity_related': [
                'cyp_inhibition', 'toxicity_score'
            ]
        }
    
    def load_data(self, filepath: str) -> pd.DataFrame:
        """
        Load the synthetic dataset.
        
        Args:
            filepath: Path to the CSV file
            
        Returns:
            Loaded DataFrame
        """
        if not os.path.exists(filepath):
            raise FileNotFoundError(f"Dataset not found at {filepath}")
        
        df = pd.read_csv(filepath)
        print(f"Loaded dataset with {len(df)} compounds and {len(df.columns)} features")
        return df
    
    def create_derived_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Create derived features that might be important for neurotoxicity prediction.
        
        Args:
            df: Input DataFrame
            
        Returns:
            DataFrame with additional derived features
        """
        df_enhanced = df.copy()
        
        # Lipinski's Rule of Five violations (drug-likeness)
        df_enhanced['lipinski_violations'] = (
            (df['molecular_weight'] > 500).astype(int) +
            (df['logp'] > 5).astype(int) +
            (df['hbd_count'] > 5).astype(int) +
            (df['hba_count'] > 10).astype(int)
        )
        
        # BBB penetration likelihood (combination of key factors)
        df_enhanced['bbb_likelihood'] = (
            df['bbb_permeability'] * 0.6 +
            np.clip((5 - df['polar_surface_area'] / 40), 0, 5) / 5 * 0.2 +
            np.clip((df['logp'] + 1) / 6, 0, 1) * 0.2
        )
        
        # Molecular complexity ratio
        df_enhanced['complexity_per_mw'] = df['complexity'] / df['molecular_weight']
        
        # Hydrogen bonding potential
        df_enhanced['h_bonding_potential'] = df['hbd_count'] + df['hba_count']
        
        # Flexibility index
        df_enhanced['flexibility_index'] = df['rotatable_bonds'] / (df['molecular_weight'] / 100)
        
        # Toxicity risk score (combination of multiple factors)
        df_enhanced['toxicity_risk'] = (
            df['toxicity_score'] * 0.4 +
            df['bbb_permeability'] * 0.3 +
            df['cyp_inhibition'] * 0.2 +
            (df_enhanced['lipinski_violations'] / 4) * 0.1
        )
        
        return df_enhanced
    
    def prepare_features(self, df: pd.DataFrame, 
                        scale_features: bool = True,
                        select_features: bool = False,
                        n_features: int = 15) -> Tuple[pd.DataFrame, pd.Series]:
        """
        Prepare features for machine learning.
        
        Args:
            df: Input DataFrame
            scale_features: Whether to scale features
            select_features: Whether to perform feature selection
            n_features: Number of features to select (if select_features=True)
            
        Returns:
            Tuple of (features DataFrame, target Series)
        """
        # Create derived features
        df_enhanced = self.create_derived_features(df)
        
        # Separate features and target
        feature_columns = [col for col in df_enhanced.columns 
                          if col not in ['compound_id', self.target_column, 'neurotox_probability']]
        
        X = df_enhanced[feature_columns].copy()
        y = df_enhanced[self.target_column].copy()
        
        # Handle any missing values
        X = X.fillna(X.median())
        
        # Feature scaling
        if scale_features:
            if self.scaler is None:
                self.scaler = StandardScaler()
                X_scaled = self.scaler.fit_transform(X)
            else:
                X_scaled = self.scaler.transform(X)
            
            X = pd.DataFrame(X_scaled, columns=X.columns, index=X.index)
        
        # Feature selection
        if select_features and n_features < len(feature_columns):
            if self.feature_selector is None:
                self.feature_selector = SelectKBest(score_func=f_classif, k=n_features)
                X_selected = self.feature_selector.fit_transform(X, y)
                selected_features = X.columns[self.feature_selector.get_support()]
            else:
                X_selected = self.feature_selector.transform(X)
                selected_features = self.feature_names
            
            X = pd.DataFrame(X_selected, columns=selected_features, index=X.index)
            self.feature_names = selected_features
        else:
            self.feature_names = X.columns
        
        return X, y
    
    def split_data(self, X: pd.DataFrame, y: pd.Series, 
                   test_size: float = 0.2, 
                   val_size: float = 0.1,
                   random_state: int = 42) -> Dict[str, pd.DataFrame]:
        """
        Split data into train, validation, and test sets.
        
        Args:
            X: Features DataFrame
            y: Target Series
            test_size: Proportion for test set
            val_size: Proportion for validation set (from remaining data)
            random_state: Random seed
            
        Returns:
            Dictionary with train, validation, and test sets
        """
        # First split: separate test set
        X_temp, X_test, y_temp, y_test = train_test_split(
            X, y, test_size=test_size, random_state=random_state, stratify=y
        )
        
        # Second split: separate train and validation from remaining data
        val_size_adjusted = val_size / (1 - test_size)  # Adjust for remaining data
        X_train, X_val, y_train, y_val = train_test_split(
            X_temp, y_temp, test_size=val_size_adjusted, 
            random_state=random_state, stratify=y_temp
        )
        
        return {
            'X_train': X_train, 'y_train': y_train,
            'X_val': X_val, 'y_val': y_val,
            'X_test': X_test, 'y_test': y_test
        }
    
    def get_feature_importance_scores(self, X: pd.DataFrame, y: pd.Series) -> pd.DataFrame:
        """
        Calculate feature importance using multiple methods.
        
        Args:
            X: Features DataFrame
            y: Target Series
            
        Returns:
            DataFrame with feature importance scores
        """
        # F-statistic scores
        f_scores, f_pvalues = f_classif(X, y)
        
        # Mutual information scores
        mi_scores = mutual_info_classif(X, y, random_state=42)
        
        # Create importance DataFrame
        importance_df = pd.DataFrame({
            'feature': X.columns,
            'f_statistic': f_scores,
            'f_pvalue': f_pvalues,
            'mutual_info': mi_scores
        })
        
        # Normalize scores for comparison
        importance_df['f_statistic_norm'] = (
            importance_df['f_statistic'] / importance_df['f_statistic'].max()
        )
        importance_df['mutual_info_norm'] = (
            importance_df['mutual_info'] / importance_df['mutual_info'].max()
        )
        
        # Combined importance score
        importance_df['combined_importance'] = (
            importance_df['f_statistic_norm'] * 0.5 + 
            importance_df['mutual_info_norm'] * 0.5
        )
        
        return importance_df.sort_values('combined_importance', ascending=False)
    
    def save_processed_data(self, data_splits: Dict, output_dir: str = 'data/train_test_split'):
        """
        Save processed data splits to files.
        
        Args:
            data_splits: Dictionary with train/val/test splits
            output_dir: Directory to save files
        """
        os.makedirs(output_dir, exist_ok=True)
        
        for split_name, data in data_splits.items():
            filepath = os.path.join(output_dir, f'{split_name}.csv')
            data.to_csv(filepath, index=False)
        
        print(f"Saved processed data splits to {output_dir}")


def main():
    """Process the synthetic dataset and create train/test splits."""
    print("Processing synthetic neurotoxicity dataset...")
    
    # Initialize feature engineer
    fe = FeatureEngineer()
    
    # Load data
    df = fe.load_data('data/raw_molecular_data.csv')
    
    # Prepare features
    print("Preparing features...")
    X, y = fe.prepare_features(df, scale_features=True, select_features=False)
    
    # Split data
    print("Splitting data...")
    data_splits = fe.split_data(X, y)
    
    # Calculate feature importance
    print("Calculating feature importance...")
    importance_df = fe.get_feature_importance_scores(X, y)
    
    # Save processed data
    fe.save_processed_data(data_splits)
    
    # Save feature importance
    importance_df.to_csv('data/feature_importance.csv', index=False)
    
    # Print summary
    print(f"\nData processing summary:")
    print(f"Total features: {len(X.columns)}")
    print(f"Training samples: {len(data_splits['X_train'])}")
    print(f"Validation samples: {len(data_splits['X_val'])}")
    print(f"Test samples: {len(data_splits['X_test'])}")
    
    print(f"\nTop 5 most important features:")
    for i, row in importance_df.head().iterrows():
        print(f"{row['feature']}: {row['combined_importance']:.3f}")
    
    print("\nFeature processing complete!")


if __name__ == "__main__":
    main()
