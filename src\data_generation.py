"""
Synthetic Neurotoxicity Data Generation Module

This module generates realistic synthetic molecular descriptor data and corresponding
neurotoxicity labels based on established structure-activity relationships (SAR)
principles in neurotoxicology.

Author: Intel ISEF Project - NeuroToxPredict
"""

import numpy as np
import pandas as pd
from typing import Tuple, Dict
import random


class NeuroToxDataGenerator:
    """
    Generates synthetic molecular descriptor data for neurotoxicity prediction.
    
    Based on established principles:
    - Blood-brain barrier penetration correlates with neurotoxicity
    - Lipophilicity (LogP) affects brain accumulation
    - Molecular weight influences transport
    - Polar surface area affects membrane permeability
    """
    
    def __init__(self, random_seed: int = 42):
        """Initialize the data generator with a random seed for reproducibility."""
        np.random.seed(random_seed)
        random.seed(random_seed)
        
        # Define realistic ranges for molecular descriptors based on drug-like compounds
        self.descriptor_ranges = {
            'molecular_weight': (150, 800),      # Daltons
            'logp': (-2, 6),                     # Lipophilicity
            'polar_surface_area': (20, 200),    # Ų
            'hbd_count': (0, 8),                 # Hydrogen bond donors
            'hba_count': (0, 15),                # Hydrogen bond acceptors
            'rotatable_bonds': (0, 20),          # Flexibility
            'aromatic_rings': (0, 5),            # Aromaticity
            'formal_charge': (-2, 2),            # Net charge
            'complexity': (50, 1000),            # Structural complexity
            'bbb_permeability': (0, 1),          # Blood-brain barrier score
            'cyp_inhibition': (0, 1),            # Cytochrome P450 inhibition
            'protein_binding': (0.1, 0.99),     # Plasma protein binding
            'half_life': (0.5, 48),              # Hours
            'bioavailability': (0.1, 1.0),      # Fraction absorbed
            'toxicity_score': (0, 1)             # General toxicity indicator
        }
    
    def generate_molecular_descriptors(self, n_compounds: int = 10000) -> pd.DataFrame:
        """
        Generate synthetic molecular descriptors for n_compounds.
        
        Args:
            n_compounds: Number of compounds to generate
            
        Returns:
            DataFrame with molecular descriptors
        """
        data = {}
        
        # Generate correlated molecular descriptors
        for descriptor, (min_val, max_val) in self.descriptor_ranges.items():
            if descriptor == 'molecular_weight':
                # Base molecular weight distribution
                data[descriptor] = np.random.lognormal(
                    mean=np.log(350), sigma=0.5, size=n_compounds
                ).clip(min_val, max_val)
            
            elif descriptor == 'logp':
                # LogP correlated with molecular weight
                mw_normalized = (data['molecular_weight'] - 150) / (800 - 150)
                data[descriptor] = (
                    np.random.normal(0, 1, n_compounds) * 1.5 + 
                    mw_normalized * 4 - 1
                ).clip(min_val, max_val)
            
            elif descriptor == 'polar_surface_area':
                # PSA inversely correlated with LogP
                logp_effect = (6 - data['logp']) / 8  # Normalize and invert
                data[descriptor] = (
                    np.random.exponential(scale=50, size=n_compounds) + 
                    logp_effect * 80 + 20
                ).clip(min_val, max_val)
            
            elif descriptor == 'hbd_count':
                # Hydrogen bond donors - discrete values
                psa_effect = data['polar_surface_area'] / 200
                data[descriptor] = np.random.poisson(
                    lam=psa_effect * 3, size=n_compounds
                ).clip(min_val, max_val)
            
            elif descriptor == 'hba_count':
                # Hydrogen bond acceptors - correlated with PSA and HBD
                base_hba = data['hbd_count'] * 1.5
                psa_effect = data['polar_surface_area'] / 200 * 5
                data[descriptor] = (
                    np.random.poisson(lam=base_hba + psa_effect, size=n_compounds)
                ).clip(min_val, max_val)
            
            elif descriptor == 'rotatable_bonds':
                # Flexibility correlated with molecular weight
                mw_effect = data['molecular_weight'] / 800 * 15
                data[descriptor] = np.random.poisson(
                    lam=mw_effect + 2, size=n_compounds
                ).clip(min_val, max_val)
            
            elif descriptor == 'aromatic_rings':
                # Aromaticity affects LogP
                logp_effect = np.maximum(0, data['logp']) / 6 * 3
                data[descriptor] = np.random.poisson(
                    lam=logp_effect + 1, size=n_compounds
                ).clip(min_val, max_val)
            
            elif descriptor == 'bbb_permeability':
                # Key neurotoxicity predictor - based on LogP and PSA
                logp_contribution = np.clip((data['logp'] + 1) / 7, 0, 1)
                psa_contribution = np.clip((200 - data['polar_surface_area']) / 180, 0, 1)
                mw_contribution = np.clip((500 - data['molecular_weight']) / 350, 0, 1)
                
                base_bbb = (logp_contribution * 0.4 + 
                           psa_contribution * 0.4 + 
                           mw_contribution * 0.2)
                
                # Add noise
                data[descriptor] = (
                    base_bbb + np.random.normal(0, 0.15, n_compounds)
                ).clip(min_val, max_val)
            
            else:
                # Generate other descriptors with some correlation to existing ones
                if descriptor in ['cyp_inhibition', 'toxicity_score']:
                    # Correlated with lipophilicity and size
                    base_value = (data['logp'] / 6 + data['molecular_weight'] / 800) / 2
                    data[descriptor] = (
                        base_value + np.random.normal(0, 0.2, n_compounds)
                    ).clip(min_val, max_val)
                else:
                    # Random generation for remaining descriptors
                    data[descriptor] = np.random.uniform(
                        min_val, max_val, n_compounds
                    )
        
        # Create compound IDs
        data['compound_id'] = [f'CMPD_{i:06d}' for i in range(n_compounds)]
        
        return pd.DataFrame(data)
    
    def generate_neurotoxicity_labels(self, descriptors_df: pd.DataFrame) -> pd.DataFrame:
        """
        Generate neurotoxicity labels based on molecular descriptors using SAR principles.
        
        Args:
            descriptors_df: DataFrame with molecular descriptors
            
        Returns:
            DataFrame with added neurotoxicity labels
        """
        df = descriptors_df.copy()
        
        # Neurotoxicity prediction based on established SAR principles
        # Key factors: BBB permeability, lipophilicity, molecular weight, toxicity indicators
        
        # Calculate neurotoxicity probability
        bbb_effect = df['bbb_permeability'] * 0.35        # High BBB penetration increases risk
        logp_effect = np.clip(df['logp'] / 6, 0, 1) * 0.25  # Moderate lipophilicity increases risk
        mw_effect = np.clip((df['molecular_weight'] - 200) / 400, 0, 1) * 0.15  # Larger molecules
        toxicity_effect = df['toxicity_score'] * 0.15      # General toxicity indicator
        cyp_effect = df['cyp_inhibition'] * 0.10           # CYP inhibition can lead to accumulation
        
        # Protective factors
        psa_protection = np.clip((df['polar_surface_area'] - 60) / 140, 0, 1) * 0.1  # High PSA reduces BBB penetration
        
        neurotox_probability = (
            bbb_effect + logp_effect + mw_effect + toxicity_effect + cyp_effect - psa_protection
        )
        
        # Add some noise to make it more realistic
        neurotox_probability += np.random.normal(0, 0.1, len(df))
        neurotox_probability = np.clip(neurotox_probability, 0, 1)
        
        # Convert probability to binary classification (threshold at 0.5)
        df['neurotoxic'] = (neurotox_probability > 0.5).astype(int)
        df['neurotox_probability'] = neurotox_probability
        
        # Add some class imbalance (neurotoxic compounds are less common)
        # Adjust threshold to get approximately 30% positive cases
        threshold = np.percentile(neurotox_probability, 70)
        df['neurotoxic'] = (neurotox_probability > threshold).astype(int)
        
        return df


def main():
    """Generate and save synthetic neurotoxicity dataset."""
    print("Generating synthetic neurotoxicity dataset...")
    
    # Initialize generator
    generator = NeuroToxDataGenerator(random_seed=42)
    
    # Generate molecular descriptors
    print("Generating molecular descriptors...")
    descriptors = generator.generate_molecular_descriptors(n_compounds=10000)
    
    # Generate neurotoxicity labels
    print("Generating neurotoxicity labels...")
    full_dataset = generator.generate_neurotoxicity_labels(descriptors)
    
    # Save datasets
    print("Saving datasets...")
    full_dataset.to_csv('data/raw_molecular_data.csv', index=False)
    
    # Print summary statistics
    print(f"\nDataset Summary:")
    print(f"Total compounds: {len(full_dataset)}")
    print(f"Neurotoxic compounds: {full_dataset['neurotoxic'].sum()} ({full_dataset['neurotoxic'].mean():.1%})")
    print(f"Non-neurotoxic compounds: {(1-full_dataset['neurotoxic']).sum()} ({(1-full_dataset['neurotoxic']).mean():.1%})")
    
    print(f"\nDescriptor ranges:")
    for col in ['molecular_weight', 'logp', 'polar_surface_area', 'bbb_permeability']:
        print(f"{col}: {full_dataset[col].min():.2f} - {full_dataset[col].max():.2f}")
    
    print("\nDataset generation complete!")


if __name__ == "__main__":
    main()
